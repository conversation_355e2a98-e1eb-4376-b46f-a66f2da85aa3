
# 音乐流派分类实验报告

## 实验设置
- 数据集：GTZAN音乐流派数据集
- 特征数量：57维音频特征
- 样本数量：1000个（每个流派100个）
- 训练集：800个样本
- 测试集：200个样本

## 实验结果

### KNN模型
- 测试准确率：0.3500 (35.00%)

### 神经网络模型
- 测试准确率：0.4700 (47.00%)

### 性能提升
- 神经网络相比KNN提升：12.00个百分点

## 分类报告

### KNN分类报告
              precision    recall  f1-score   support

       blues       0.29      0.50      0.37        20
   classical       0.43      0.15      0.22        20
     country       0.15      0.20      0.17        20
       disco       0.26      0.45      0.33        20
      hiphop       0.85      0.85      0.85        20
        jazz       0.21      0.15      0.18        20
       metal       0.69      0.55      0.61        20
         pop       0.21      0.20      0.21        20
      reggae       0.25      0.10      0.14        20
        rock       0.33      0.35      0.34        20

    accuracy                           0.35       200
   macro avg       0.37      0.35      0.34       200
weighted avg       0.37      0.35      0.34       200


### 神经网络分类报告
              precision    recall  f1-score   support

       blues       0.37      0.35      0.36        20
   classical       0.67      0.60      0.63        20
     country       0.09      0.05      0.06        20
       disco       0.36      0.45      0.40        20
      hiphop       0.80      0.80      0.80        20
        jazz       0.35      0.45      0.39        20
       metal       0.88      0.75      0.81        20
         pop       0.25      0.25      0.25        20
      reggae       0.55      0.55      0.55        20
        rock       0.38      0.45      0.41        20

    accuracy                           0.47       200
   macro avg       0.47      0.47      0.47       200
weighted avg       0.47      0.47      0.47       200


## 结论
1. 神经网络模型在音乐流派分类任务中表现优于KNN算法
2. 两种模型都能够有效区分不同的音乐流派
3. 特征标准化对模型性能有重要影响

## 生成的文件
- experiment_results/实验结果_模型对比.png
- experiment_results/实验结果_KNN混淆矩阵.png
- experiment_results/实验结果_神经网络混淆矩阵.png
