#!/usr/bin/env python3
"""
基于实际数据的音乐流派特征分析图表生成
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

def load_data():
    """加载数据"""
    print("加载实际数据...")
    df = pd.read_csv('data/Data/features_30_sec.csv')
    print(f"数据形状: {df.shape}")
    return df

def create_genre_tempo_analysis(df):
    """创建流派节拍分析图"""
    print("生成流派节拍特征分析...")
    
    plt.figure(figsize=(14, 8))
    
    # 创建箱线图显示各流派的节拍分布
    sns.boxplot(data=df, x='label', y='tempo', palette='Set3')
    plt.title('不同音乐流派的节拍速度分布（基于实际数据）', fontsize=16, fontweight='bold')
    plt.xlabel('音乐流派', fontsize=12)
    plt.ylabel('节拍速度 (BPM)', fontsize=12)
    plt.xticks(rotation=45)
    
    # 添加均值点
    genre_means = df.groupby('label')['tempo'].mean()
    for i, (genre, mean_tempo) in enumerate(genre_means.items()):
        plt.scatter(i, mean_tempo, color='red', s=100, marker='D', zorder=5)
        plt.text(i, mean_tempo + 5, f'{mean_tempo:.1f}', ha='center', va='bottom', 
                fontweight='bold', color='red')
    
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('paper_figures/流派节拍分析.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_spectral_features_comparison(df):
    """创建频谱特征对比图"""
    print("生成频谱特征对比分析...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    spectral_features = [
        ('spectral_centroid_mean', '频谱质心'),
        ('spectral_bandwidth_mean', '频谱带宽'),
        ('rolloff_mean', '频谱滚降'),
        ('rms_mean', 'RMS能量')
    ]
    
    for idx, (feature, title) in enumerate(spectral_features):
        ax = axes[idx//2, idx%2]
        
        # 创建小提琴图
        sns.violinplot(data=df, x='label', y=feature, ax=ax, palette='viridis')
        ax.set_title(f'{title}分布', fontsize=14, fontweight='bold')
        ax.set_xlabel('音乐流派', fontsize=12)
        ax.set_ylabel(title, fontsize=12)
        ax.tick_params(axis='x', rotation=45)
        ax.grid(True, alpha=0.3)
    
    plt.suptitle('不同音乐流派的频谱特征对比（基于实际数据）', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('paper_figures/频谱特征对比.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_mfcc_radar_chart(df):
    """创建MFCC特征雷达图"""
    print("生成MFCC特征雷达图...")
    
    # 选择前8个MFCC特征
    mfcc_features = [f'mfcc{i}_mean' for i in range(1, 9)]
    
    # 计算各流派的MFCC均值并标准化
    genre_mfcc = df.groupby('label')[mfcc_features].mean()
    
    # 标准化到0-1范围
    from sklearn.preprocessing import MinMaxScaler
    scaler = MinMaxScaler()
    genre_mfcc_scaled = pd.DataFrame(
        scaler.fit_transform(genre_mfcc.T).T,
        index=genre_mfcc.index,
        columns=genre_mfcc.columns
    )
    
    # 选择几个代表性流派
    selected_genres = ['classical', 'hiphop', 'metal', 'jazz', 'reggae']
    
    # 创建雷达图
    angles = np.linspace(0, 2*np.pi, len(mfcc_features), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形
    
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    colors = plt.cm.Set2(np.linspace(0, 1, len(selected_genres)))
    
    for i, genre in enumerate(selected_genres):
        if genre in genre_mfcc_scaled.index:
            values = genre_mfcc_scaled.loc[genre].tolist()
            values += values[:1]  # 闭合图形
            
            ax.plot(angles, values, 'o-', linewidth=2, label=genre, color=colors[i])
            ax.fill(angles, values, alpha=0.25, color=colors[i])
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels([f'MFCC{i}' for i in range(1, 9)])
    ax.set_ylim(0, 1)
    ax.set_title('不同音乐流派的MFCC特征雷达图', fontsize=16, fontweight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax.grid(True)
    
    plt.tight_layout()
    plt.savefig('paper_figures/MFCC雷达图.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_feature_distribution_by_genre(df):
    """创建按流派分组的特征分布图"""
    print("生成按流派分组的特征分布...")
    
    # 选择几个关键特征
    key_features = ['tempo', 'spectral_centroid_mean', 'rms_mean', 'mfcc1_mean']
    feature_names = ['节拍速度', '频谱质心', 'RMS能量', 'MFCC1']
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.ravel()
    
    for i, (feature, name) in enumerate(zip(key_features, feature_names)):
        ax = axes[i]
        
        # 为每个流派创建直方图
        genres = df['label'].unique()
        colors = plt.cm.tab10(np.linspace(0, 1, len(genres)))
        
        for j, genre in enumerate(genres):
            genre_data = df[df['label'] == genre][feature]
            ax.hist(genre_data, alpha=0.6, label=genre, color=colors[j], bins=15)
        
        ax.set_title(f'{name}在不同流派中的分布', fontsize=14, fontweight='bold')
        ax.set_xlabel(name, fontsize=12)
        ax.set_ylabel('频次', fontsize=12)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
    
    plt.suptitle('关键特征在不同音乐流派中的分布对比', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('paper_figures/特征分布对比.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_correlation_heatmap(df):
    """创建特征相关性热力图"""
    print("生成特征相关性热力图...")
    
    # 选择主要特征
    main_features = [
        'tempo', 'spectral_centroid_mean', 'spectral_bandwidth_mean',
        'rolloff_mean', 'zero_crossing_rate_mean', 'rms_mean',
        'chroma_stft_mean', 'harmony_mean', 'perceptr_mean',
        'mfcc1_mean', 'mfcc2_mean', 'mfcc3_mean', 'mfcc4_mean', 'mfcc5_mean'
    ]
    
    # 计算相关性矩阵
    corr_matrix = df[main_features].corr()
    
    plt.figure(figsize=(12, 10))
    
    # 创建热力图
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, mask=mask, annot=True, fmt='.2f', cmap='RdBu_r',
                center=0, square=True, cbar_kws={'label': '相关系数'})
    
    plt.title('主要音频特征相关性热力图（基于实际数据）', fontsize=16, fontweight='bold')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    
    plt.tight_layout()
    plt.savefig('paper_figures/特征相关性热力图.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_genre_statistics_table(df):
    """创建流派统计表格图"""
    print("生成流派统计表格...")
    
    # 计算各流派的关键统计信息
    key_features = ['tempo', 'spectral_centroid_mean', 'rms_mean', 'mfcc1_mean']
    
    stats_data = []
    for genre in sorted(df['label'].unique()):
        genre_data = df[df['label'] == genre]
        row = [genre]
        for feature in key_features:
            mean_val = genre_data[feature].mean()
            std_val = genre_data[feature].std()
            row.extend([mean_val, std_val])
        stats_data.append(row)
    
    # 创建表格
    columns = ['流派']
    for feature in key_features:
        feature_names = {
            'tempo': '节拍',
            'spectral_centroid_mean': '频谱质心',
            'rms_mean': 'RMS能量',
            'mfcc1_mean': 'MFCC1'
        }
        columns.extend([f'{feature_names[feature]}_均值', f'{feature_names[feature]}_标准差'])
    
    stats_df = pd.DataFrame(stats_data, columns=columns)
    
    # 创建表格图
    fig, ax = plt.subplots(figsize=(16, 8))
    ax.axis('tight')
    ax.axis('off')
    
    # 格式化数值
    formatted_data = []
    for _, row in stats_df.iterrows():
        formatted_row = [row[0]]  # 流派名
        for i in range(1, len(row), 2):
            mean_val = f"{row[i]:.1f}"
            std_val = f"{row[i+1]:.1f}"
            formatted_row.extend([mean_val, std_val])
        formatted_data.append(formatted_row)
    
    table = ax.table(cellText=formatted_data, colLabels=columns, 
                    cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.5)
    
    # 设置表格样式
    for i in range(len(columns)):
        table[(0, i)].set_facecolor('#40466e')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    plt.title('各音乐流派关键特征统计表（基于实际数据）', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.savefig('paper_figures/流派统计表.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    print("=" * 60)
    print("基于实际数据的音乐流派特征分析")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs("paper_figures", exist_ok=True)
    
    # 加载数据
    df = load_data()
    
    # 生成各种分析图表
    create_genre_tempo_analysis(df)
    create_spectral_features_comparison(df)
    create_mfcc_radar_chart(df)
    create_feature_distribution_by_genre(df)
    create_correlation_heatmap(df)
    create_genre_statistics_table(df)
    
    print("\n" + "=" * 60)
    print("基于实际数据的分析图表生成完成！")
    print("新增图表文件：")
    print("  - 流派节拍分析.png")
    print("  - 频谱特征对比.png")
    print("  - MFCC雷达图.png")
    print("  - 特征分布对比.png")
    print("  - 特征相关性热力图.png")
    print("  - 流派统计表.png")
    print("=" * 60)

if __name__ == "__main__":
    main()
