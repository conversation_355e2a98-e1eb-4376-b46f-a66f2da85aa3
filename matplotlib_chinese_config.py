
# matplotlib中文字体配置文件
# 将此内容添加到您的Python脚本开头

import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 方法1: 设置中文字体（推荐）
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 方法2: 如果方法1不工作，尝试这个
# plt.rcParams['font.family'] = 'DejaVu Sans'
# plt.rcParams['axes.unicode_minus'] = False

# 方法3: 使用特定字体文件（如果下载了字体）
# font_path = "fonts/SourceHanSansSC-Regular.otf"
# if os.path.exists(font_path):
#     fm.fontManager.addfont(font_path)
#     font_prop = fm.FontProperties(fname=font_path)
#     plt.rcParams['font.sans-serif'] = [font_prop.get_name()]

print("中文字体配置完成")
