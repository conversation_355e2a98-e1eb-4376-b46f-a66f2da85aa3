repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.1
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-docstring-first
    -   id: check-json
    -   id: check-yaml
    -   id: debug-statements
    -   id: name-tests-test
    -   id: requirements-txt-fixer
-   repo: https://github.com/PyCQA/flake8
    rev: 3.9.2
    hooks:
    -   id: flake8
        additional_dependencies: [flake8-typing-imports==1.10.0]
-   repo: https://github.com/pre-commit/mirrors-autopep8
    rev: v1.5.7
    hooks:
    -   id: autopep8
-   repo: https://github.com/pre-commit/pre-commit
    rev: v2.15.0
    hooks:
    -   id: validate_manifest
-   repo: https://github.com/asottile/pyupgrade
    rev: v2.29.0
    hooks:
    -   id: pyupgrade
        args: [--py36-plus]
-   repo: https://github.com/asottile/reorder_python_imports
    rev: v2.6.0
    hooks:
    -   id: reorder-python-imports
        args: [--py3-plus]
-   repo: https://github.com/asottile/add-trailing-comma
    rev: v2.1.0
    hooks:
    -   id: add-trailing-comma
        args: [--py36-plus]
-   repo: https://github.com/asottile/setup-cfg-fmt
    rev: v1.17.0
    hooks:
    -   id: setup-cfg-fmt
-   repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.910
    hooks:
    -   id: mypy
        additional_dependencies: [types-all]
