Metadata-Version: 2.4
Name: music_genre_classifier
Version: 0.0.1
Summary: Music Genre Classifier -- using GTZAN dataset
Home-page: https://github.com/ryansingman/music-genre-classifier
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Project-URL: Issue Tracker, https://github.com/ryansingman/music-genre-classifier/issues
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: Implementation :: CPython
Requires-Python: >=3.9
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: keras_tuner>=1.1.0
Requires-Dist: pandas>=1.3.4
Requires-Dist: pyyaml>=6.0
Requires-Dist: sklearn>=1.0.1
Requires-Dist: tensorflow>=2.7.0
Dynamic: license-file

# music-genre-classifier
Classifies music genres based on extracted features from the GTZAN Dataset. Uses KNN and Deep NN for classification.

## Build Instructions
You can install the `music_genre_classifier` package as follows:

```
python3 -m pip install --upgrade build
python3 -m build
python3 -m pip install -e .
```

## Development Instructions
Install pre-commit to ensure code quality.
```
pre-commit install
```

This will run checks on your code every time you commit.

### Kaggle API
Follow the API credential steps [here]{https://github.com/Kaggle/kaggle-api#api-credentials} to download the dataset from Kaggle.

## Training Instructions
To train a model, run the following command:

```
python -m music_genre_classifier <path_to_config> --display_results
```

For example, to train only the KNN model, you would run:
```
python -m music_genre_classifier configs/knn_only.yaml --display_results
```
