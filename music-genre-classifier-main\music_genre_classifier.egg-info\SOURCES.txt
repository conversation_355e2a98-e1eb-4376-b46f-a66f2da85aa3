LICENSE
README.md
pyproject.toml
setup.cfg
setup.py
music_genre_classifier/__init__.py
music_genre_classifier/__main__.py
music_genre_classifier.egg-info/PKG-INFO
music_genre_classifier.egg-info/SOURCES.txt
music_genre_classifier.egg-info/dependency_links.txt
music_genre_classifier.egg-info/requires.txt
music_genre_classifier.egg-info/top_level.txt
music_genre_classifier/dataset/__init__.py
music_genre_classifier/dataset/create_dataset.py
music_genre_classifier/dataset/split_dataset.py
music_genre_classifier/models/__init__.py
music_genre_classifier/models/base.py
music_genre_classifier/models/builder.py
music_genre_classifier/models/knn.py
music_genre_classifier/models/neural_net.py
tests/__init__.py
tests/dataset_test.py