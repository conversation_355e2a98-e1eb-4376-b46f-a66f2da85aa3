import pathlib
from typing import List

import numpy as np
import pandas as pd


def _get_csv_file_path(data_dir: str, three_sec_song: bool) -> pathlib.Path:
    """Gets CSV file path from data directory.

    Parameters
    ----------
    data_dir : str
        directory where datafiles are stored
    three_sec_song : bool
        if should use the three second song csv (as opposed to thirty second songs)

    Returns
    -------
    pathlib.Path
        path to csv file
    """
    if three_sec_song:
        return pathlib.Path(data_dir).joinpath(pathlib.Path("Data/features_3_sec.csv"))
    else:
        return pathlib.Path(data_dir).joinpath(pathlib.Path("Data/features_30_sec.csv"))


def _download_dataset(gtzan_url: str, data_dir: str) -> None:
    """Downloads dataset from kaggle, deleting contents of data_dir.

    Parameters
    ----------
    gtzan_url : str
        url for gtzan kaggle dataset (not used in local mode)
    data_dir : str
        directory to download gtzan dataset to
    """
    print(f"数据集未找到在 {data_dir}")
    print("请按照以下步骤准备本地数据集：")
    print("1. 下载GTZAN数据集特征文件：")
    print("   - features_30_sec.csv")
    print("   - features_3_sec.csv")
    print("2. 创建目录结构：")
    print(f"   {data_dir}/")
    print("   └── Data/")
    print("       ├── features_30_sec.csv")
    print("       └── features_3_sec.csv")
    print("3. 将CSV文件放到正确位置")

    raise FileNotFoundError(f"请手动准备数据集到 {data_dir}/Data/ 目录")


def create_gtzan_dataset(
    gtzan_url: str,
    data_dir: str,
    features: List[str],
    three_sec_songs: bool = True,
) -> np.ndarray:
    """Creates GTZAN dataset.

    Parameters
    ----------
    gtzan_url : str
        url for gtzan kaggle dataset
    data_dir : str
        directory to download gtzan dataset to
    features : List[str]
        list of dataset features to include
    batch_size: int
        size of batch for dataset
    three_sec_songs : bool, optional
        if should use the three second songs (instead of 30 second songs), by default False

    Returns
    -------
    np.ndarray
        numpy array composed of the requested features, labels
    """
    # check if dataset is not yet downloaded
    if not _get_csv_file_path(data_dir, three_sec_songs).exists():
        # download dataset
        _download_dataset(gtzan_url, data_dir)

    # load csv into numpy array
    dataset_arr = pd.read_csv(
        str(_get_csv_file_path(data_dir, three_sec_songs)),
        names=features + ["label"],
    )[1:].to_numpy()

    # convert categorical labels to ints
    _, dataset_arr[:, -1] = np.unique(dataset_arr[:, -1], return_inverse=True)

    # return np dataset
    return dataset_arr.astype(np.float64)
