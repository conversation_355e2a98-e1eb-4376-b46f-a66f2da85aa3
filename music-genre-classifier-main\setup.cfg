[metadata]
name = music_genre_classifier
version = 0.0.1
description = Music Genre Classifier -- using GTZAN dataset
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/ryansingman/music-genre-classifier
author = <PERSON>
author_email = <EMAIL>
license = MIT
license_file = LICENSE
classifiers =
    License :: OSI Approved :: MIT License
    Operating System :: OS Independent
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3 :: Only
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: Implementation :: CPython
project_urls =
    Issue Tracker = https://github.com/ryansingman/music-genre-classifier/issues

[options]
packages = find:
install_requires =
    keras_tuner>=1.1.0
    pandas>=1.3.4
    pyyaml>=6.0
    sklearn>=1.0.1
    tensorflow>=2.7.0
python_requires = >=3.9
extras_requires =
    pytest >= 6.2.5

[flake8]
max-line-length = 100
per-file-ignores = __init__.py:F401
