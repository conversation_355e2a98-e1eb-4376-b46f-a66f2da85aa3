#!/usr/bin/env python3
"""
简化的音乐流派分类实验
直接使用scikit-learn进行KNN和简单神经网络实验
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.neighbors import KNeighborsClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_prepare_data():
    """加载和预处理数据"""
    print("加载数据...")
    
    # 加载数据
    df = pd.read_csv('data/Data/features_30_sec.csv')
    print(f"数据形状: {df.shape}")
    print(f"流派分布: {df['label'].value_counts().to_dict()}")
    
    # 分离特征和标签
    X = df.drop('label', axis=1).values
    y = df['label'].values
    
    # 标签编码
    le = LabelEncoder()
    y_encoded = le.fit_transform(y)
    
    print(f"特征维度: {X.shape}")
    print(f"类别数量: {len(le.classes_)}")
    print(f"类别映射: {dict(zip(le.classes_, range(len(le.classes_))))}")
    
    return X, y_encoded, le

def split_and_scale_data(X, y):
    """数据划分和标准化"""
    print("\n数据划分和标准化...")
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"训练集大小: {X_train.shape}")
    print(f"测试集大小: {X_test.shape}")
    
    # 特征标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    return X_train_scaled, X_test_scaled, y_train, y_test, scaler

def train_knn_model(X_train, X_test, y_train, y_test):
    """训练KNN模型"""
    print("\n=" * 50)
    print("训练KNN模型")
    print("=" * 50)
    
    # 超参数搜索
    print("进行超参数搜索...")
    param_grid = {
        'n_neighbors': [3, 5, 7, 9, 11],
        'weights': ['uniform', 'distance'],
        'metric': ['euclidean', 'manhattan']
    }
    
    knn = KNeighborsClassifier()
    grid_search = GridSearchCV(knn, param_grid, cv=5, scoring='accuracy', n_jobs=-1)
    grid_search.fit(X_train, y_train)
    
    print(f"最佳参数: {grid_search.best_params_}")
    print(f"最佳交叉验证分数: {grid_search.best_score_:.4f}")
    
    # 在测试集上评估
    best_knn = grid_search.best_estimator_
    y_pred = best_knn.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"测试集准确率: {accuracy:.4f}")
    
    return best_knn, y_pred, accuracy

def train_mlp_model(X_train, X_test, y_train, y_test):
    """训练多层感知机模型"""
    print("\n=" * 50)
    print("训练神经网络模型")
    print("=" * 50)
    
    # 超参数搜索
    print("进行超参数搜索...")
    param_grid = {
        'hidden_layer_sizes': [(50,), (100,), (50, 50), (100, 50)],
        'learning_rate_init': [0.001, 0.01, 0.1],
        'alpha': [0.0001, 0.001, 0.01]
    }
    
    mlp = MLPClassifier(max_iter=500, random_state=42)
    grid_search = GridSearchCV(mlp, param_grid, cv=3, scoring='accuracy', n_jobs=-1)
    grid_search.fit(X_train, y_train)
    
    print(f"最佳参数: {grid_search.best_params_}")
    print(f"最佳交叉验证分数: {grid_search.best_score_:.4f}")
    
    # 在测试集上评估
    best_mlp = grid_search.best_estimator_
    y_pred = best_mlp.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"测试集准确率: {accuracy:.4f}")
    
    return best_mlp, y_pred, accuracy

def generate_results_visualization(y_test, y_pred_knn, y_pred_mlp, le, 
                                 knn_accuracy, mlp_accuracy):
    """生成结果可视化"""
    print("\n生成实验结果可视化...")
    
    # 创建结果目录
    import os
    os.makedirs("experiment_results", exist_ok=True)
    
    # 1. 模型准确率对比
    plt.figure(figsize=(10, 6))
    models = ['KNN', '神经网络']
    accuracies = [knn_accuracy * 100, mlp_accuracy * 100]
    
    bars = plt.bar(models, accuracies, color=['skyblue', 'lightcoral'], alpha=0.8)
    plt.title('模型性能对比（实验结果）', fontsize=16, fontweight='bold')
    plt.ylabel('准确率 (%)', fontsize=12)
    plt.ylim(0, 100)
    
    # 添加数值标签
    for bar, acc in zip(bars, accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'{acc:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('experiment_results/实验结果_模型对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. KNN混淆矩阵
    plt.figure(figsize=(10, 8))
    cm_knn = confusion_matrix(y_test, y_pred_knn)
    sns.heatmap(cm_knn, annot=True, fmt='d', cmap='Blues',
                xticklabels=le.classes_, yticklabels=le.classes_)
    plt.title('KNN模型混淆矩阵', fontsize=16, fontweight='bold')
    plt.xlabel('预测标签', fontsize=12)
    plt.ylabel('真实标签', fontsize=12)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('experiment_results/实验结果_KNN混淆矩阵.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 神经网络混淆矩阵
    plt.figure(figsize=(10, 8))
    cm_mlp = confusion_matrix(y_test, y_pred_mlp)
    sns.heatmap(cm_mlp, annot=True, fmt='d', cmap='Blues',
                xticklabels=le.classes_, yticklabels=le.classes_)
    plt.title('神经网络模型混淆矩阵', fontsize=16, fontweight='bold')
    plt.xlabel('预测标签', fontsize=12)
    plt.ylabel('真实标签', fontsize=12)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('experiment_results/实验结果_神经网络混淆矩阵.png', dpi=300, bbox_inches='tight')
    plt.close()

def generate_detailed_report(y_test, y_pred_knn, y_pred_mlp, le, 
                           knn_accuracy, mlp_accuracy):
    """生成详细的实验报告"""
    print("\n生成详细实验报告...")
    
    report = f"""
# 音乐流派分类实验报告

## 实验设置
- 数据集：GTZAN音乐流派数据集
- 特征数量：57维音频特征
- 样本数量：1000个（每个流派100个）
- 训练集：800个样本
- 测试集：200个样本

## 实验结果

### KNN模型
- 测试准确率：{knn_accuracy:.4f} ({knn_accuracy*100:.2f}%)

### 神经网络模型
- 测试准确率：{mlp_accuracy:.4f} ({mlp_accuracy*100:.2f}%)

### 性能提升
- 神经网络相比KNN提升：{(mlp_accuracy - knn_accuracy)*100:.2f}个百分点

## 分类报告

### KNN分类报告
{classification_report(y_test, y_pred_knn, target_names=le.classes_)}

### 神经网络分类报告
{classification_report(y_test, y_pred_mlp, target_names=le.classes_)}

## 结论
1. 神经网络模型在音乐流派分类任务中表现优于KNN算法
2. 两种模型都能够有效区分不同的音乐流派
3. 特征标准化对模型性能有重要影响

## 生成的文件
- experiment_results/实验结果_模型对比.png
- experiment_results/实验结果_KNN混淆矩阵.png
- experiment_results/实验结果_神经网络混淆矩阵.png
"""
    
    with open('experiment_results/实验报告.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("实验报告已保存到: experiment_results/实验报告.md")

def main():
    """主函数"""
    print("=" * 60)
    print("音乐流派分类实验")
    print("=" * 60)
    
    # 1. 加载和预处理数据
    X, y, le = load_and_prepare_data()
    
    # 2. 数据划分和标准化
    X_train, X_test, y_train, y_test, scaler = split_and_scale_data(X, y)
    
    # 3. 训练KNN模型
    knn_model, y_pred_knn, knn_accuracy = train_knn_model(X_train, X_test, y_train, y_test)
    
    # 4. 训练神经网络模型
    mlp_model, y_pred_mlp, mlp_accuracy = train_mlp_model(X_train, X_test, y_train, y_test)
    
    # 5. 生成可视化结果
    generate_results_visualization(y_test, y_pred_knn, y_pred_mlp, le, 
                                 knn_accuracy, mlp_accuracy)
    
    # 6. 生成详细报告
    generate_detailed_report(y_test, y_pred_knn, y_pred_mlp, le, 
                           knn_accuracy, mlp_accuracy)
    
    print("\n" + "=" * 60)
    print("实验完成！")
    print("=" * 60)
    print("结果文件保存在 experiment_results/ 目录中")
    print(f"KNN准确率: {knn_accuracy:.4f} ({knn_accuracy*100:.2f}%)")
    print(f"神经网络准确率: {mlp_accuracy:.4f} ({mlp_accuracy*100:.2f}%)")
    print(f"性能提升: {(mlp_accuracy - knn_accuracy)*100:.2f}个百分点")

if __name__ == "__main__":
    main()
