# 音乐流派分类期末作业实验说明

## 文件说明

### 主要文件
1. **音乐流派分类期末论文.md** - 完整的期末论文，按照作业模版格式编写
2. **run_experiment.py** - 完整的实验运行脚本，包含可视化图表生成
3. **test_music_classifier.py** - 简化的测试脚本，用于验证代码功能
4. **music-genre-classifier-main/** - 您的音乐分类项目代码

### 论文内容
论文包含以下章节：
- 摘要（包含研究目的、方法、内容、结论）
- 第一章：引言（问题描述、分析、相关工作）
- 第二章：数据预处理（数据分析、特征提取、归一化、数据集划分）
- 第三章：模型构建（KNN和神经网络算法描述与实现）
- 第四章：模型评估（训练结果、关键指标分析）
- 第五章：总结与展望
- 参考文献

### 需要插入的图片位置
论文中标注了16个图片插入位置：
1. GTZAN数据集流派分布饼图
2. 特征分布统计图表
3. MFCC特征可视化图
4. 标准化前后特征分布对比图
5. 数据集划分示意图
6. KNN超参数调优过程图
7. 神经网络结构示意图
8. 模型训练流程图
9. KNN超参数搜索结果热力图
10. 神经网络训练过程中的损失和准确率曲线
11. 两种模型的训练损失对比图
12. 验证集准确率变化曲线
13. KNN模型混淆矩阵
14. 神经网络模型混淆矩阵
15. 特征重要性排序图
16. 典型错误分类案例的特征分布图

## 运行步骤

### 1. 环境准备

首先确保安装了必要的Python包：

```bash
pip install numpy pandas scikit-learn tensorflow keras-tuner PyYAML kaggle matplotlib seaborn
```

### 2. 数据集准备

**方法一：下载真实GTZAN数据集（推荐）**

1. **下载数据集**：
   - 链接1：https://www.kaggle.com/datasets/andradaolteanu/gtzan-dataset-music-genre-classification
   - 链接2：http://marsyas.info/downloads/datasets.html

2. **文件重命名和放置**：
   下载后，您需要找到以下两个CSV文件：
   - `features_30_sec.csv` - 30秒音频片段的特征
   - `features_3_sec.csv` - 3秒音频片段的特征

3. **创建目录结构**：
   ```
   您的项目目录/
   ├── data/
   │   └── Data/
   │       ├── features_30_sec.csv
   │       └── features_3_sec.csv
   ├── music-genre-classifier-main/
   └── 其他文件...
   ```

**方法二：使用数据集准备脚本**

运行准备脚本：
```bash
python prepare_dataset.py
```

这个脚本会：
- 自动创建正确的目录结构
- 检查现有文件
- 可以创建示例数据用于测试代码
- 提供详细的下载说明

### 3. 运行实验

#### 方法一：使用简化测试脚本（推荐）

```bash
python test_music_classifier.py
```

这个脚本会：
- 检查环境和依赖
- 安装项目
- 验证本地数据集
- 让您选择运行哪个模型

#### 方法二：使用完整实验脚本

```bash
python run_experiment.py
```

这个脚本会：
- 生成示例可视化图表
- 可选择运行实际实验
- 生成实验报告

#### 方法三：直接运行项目代码

```bash
cd music-genre-classifier-main
pip install -e .

# 运行KNN模型
python -m music_genre_classifier configs/knn_only.yaml --display_results

# 运行神经网络模型
python -m music_genre_classifier configs/neural_net_only.yaml --display_results

# 运行完整实验
python -m music_genre_classifier configs/default.yaml --display_results
```

### 4. 查看结果

实验完成后，您可以在以下位置找到结果：
- `results/figures/` - 生成的图表文件
- `results/experiment_report.md` - 实验报告
- `trained_models/` - 训练好的模型文件

## 注意事项

### 运行时间
- KNN模型：约5-10分钟
- 神经网络模型：约15-30分钟（取决于硬件）
- 完整实验：约30-60分钟

### 可能遇到的问题

1. **数据集文件未找到**
   - 确保CSV文件放在正确的位置：`data/Data/`
   - 检查文件名是否正确：`features_30_sec.csv` 和 `features_3_sec.csv`
   - 运行 `python prepare_dataset.py` 检查和创建目录结构

2. **CSV文件格式错误**
   - 确保CSV文件包含正确的列名
   - 检查是否有缺失的数据或格式问题
   - 可以先用示例数据测试代码

3. **内存不足**
   - 如果内存不足，可以修改配置文件减少训练轮次
   - 或者使用3秒数据集（数据量更大但可以分批处理）

4. **依赖包问题**
   - 确保所有必要的包都已安装
   - 如果有版本冲突，建议使用虚拟环境

5. **CUDA/GPU问题**
   - 如果没有GPU，TensorFlow会自动使用CPU
   - 训练时间会相应增加

### 自定义配置

您可以修改配置文件来调整实验参数：

- `configs/default.yaml` - 完整实验配置
- `configs/knn_only.yaml` - 仅KNN模型
- `configs/neural_net_only.yaml` - 仅神经网络模型

主要可调参数：
- `train_epochs` - 神经网络训练轮次
- `features` - 使用的音频特征
- `three_sec_songs` - 是否使用3秒音频片段

## 论文完善建议

1. **添加图表**：运行实验后，将生成的图表插入到论文中标注的位置
2. **更新结果**：根据实际实验结果更新论文中的数值
3. **个人信息**：填写论文封面的姓名、学号等信息
4. **参考文献**：根据需要添加更多相关文献
5. **实验分析**：根据实际结果深入分析模型性能

## 技术支持

如果在运行过程中遇到问题，请检查：
1. Python版本（建议3.7+）
2. 依赖包版本兼容性
3. 网络连接状态
4. 磁盘空间（数据集约1GB）

祝您实验顺利！
