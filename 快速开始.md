# 音乐流派分类 - 快速开始指南

## 🚀 5分钟快速开始

### 步骤1：安装依赖
```bash
pip install numpy pandas scikit-learn tensorflow keras-tuner PyYAML matplotlib seaborn
```

### 步骤2：准备数据集
```bash
python prepare_dataset.py
```
选择 "1" 创建示例数据（用于快速测试）

### 步骤3：运行实验
```bash
python test_music_classifier.py
```
选择 "1" 运行KNN模型（最快）

## 📁 数据集下载链接

如果您想使用真实数据集，请从以下链接下载：

### 主要下载链接：
1. **Kaggle（推荐）**：
   https://www.kaggle.com/datasets/andradaolteanu/gtzan-dataset-music-genre-classification
   
2. **官方网站**：
   http://marsyas.info/downloads/datasets.html

### 下载后的文件处理：

1. **解压文件**：下载的通常是zip格式
2. **找到CSV文件**：在解压后的文件中找到：
   - `features_30_sec.csv`
   - `features_3_sec.csv`
3. **放置文件**：将这两个文件放到以下位置：
   ```
   您的项目目录/
   └── data/
       └── Data/
           ├── features_30_sec.csv
           └── features_3_sec.csv
   ```

### 文件重命名说明：

如果下载的文件名不同，请重命名为：
- `features_30_sec.csv` - 包含30秒音频片段的特征数据
- `features_3_sec.csv` - 包含3秒音频片段的特征数据

## 🔧 目录结构

确保您的项目目录结构如下：
```
您的项目目录/
├── data/                           # 数据目录
│   └── Data/                       # 数据子目录
│       ├── features_30_sec.csv     # 30秒特征数据
│       └── features_3_sec.csv      # 3秒特征数据
├── music-genre-classifier-main/    # 项目代码
│   ├── music_genre_classifier/
│   ├── configs/
│   └── setup.py
├── prepare_dataset.py              # 数据集准备脚本
├── test_music_classifier.py        # 测试脚本
├── run_experiment.py               # 完整实验脚本
├── 音乐流派分类期末论文.md          # 论文
└── 实验说明.md                     # 详细说明
```

## ⚡ 快速测试（无需下载真实数据）

如果您只想测试代码功能：

1. 运行数据准备脚本：
   ```bash
   python prepare_dataset.py
   ```
   
2. 选择 "1" 创建示例数据

3. 运行测试：
   ```bash
   python test_music_classifier.py
   ```

这将使用随机生成的示例数据测试所有功能。

## 📊 实验选项

### 快速测试（5-10分钟）：
```bash
python test_music_classifier.py
```
选择 "1" - 仅KNN模型

### 完整实验（30-60分钟）：
```bash
python test_music_classifier.py
```
选择 "3" - 两个模型都运行

### 生成可视化图表：
```bash
python run_experiment.py
```

## 🎯 预期结果

### KNN模型：
- 准确率：约65-70%
- 训练时间：5-10分钟
- 适合快速测试

### 神经网络模型：
- 准确率：约75-80%
- 训练时间：15-30分钟
- 更好的性能

## 🔍 验证数据集

运行以下命令验证数据集是否正确：
```bash
python -c "
import pandas as pd
import os

files = ['data/Data/features_30_sec.csv', 'data/Data/features_3_sec.csv']
for file in files:
    if os.path.exists(file):
        df = pd.read_csv(file)
        print(f'{file}: {df.shape[0]} 行, {df.shape[1]} 列')
        print(f'标签列: {df.columns[-1]}')
        print(f'流派: {df.iloc[:, -1].unique()[:5]}...')
        print()
    else:
        print(f'文件不存在: {file}')
"
```

## ❓ 常见问题

### Q: 下载的文件名不对怎么办？
A: 重命名为 `features_30_sec.csv` 和 `features_3_sec.csv`

### Q: CSV文件格式错误？
A: 确保文件包含58个特征列和1个标签列，共59列

### Q: 内存不足？
A: 使用示例数据或减少训练轮次

### Q: 没有GPU？
A: 代码会自动使用CPU，只是训练时间会长一些

## 📞 获取帮助

如果遇到问题：
1. 查看 `实验说明.md` 获取详细信息
2. 检查错误信息和日志
3. 确认目录结构和文件名正确
4. 尝试使用示例数据测试

## 🎉 完成后

实验完成后，您将得到：
- 训练好的模型
- 性能评估结果
- 可视化图表（如果运行了可视化脚本）
- 完整的论文模板

将实验结果插入到论文的相应位置，您的期末作业就完成了！
