#!/usr/bin/env python3
"""
一键生成所有16个中文图表
整合第一部分和第二部分的所有功能
"""

import os
import subprocess
import sys
import time

def 检查依赖():
    """检查必要的依赖包"""
    required_packages = ['pandas', 'matplotlib', 'seaborn', 'numpy', 'sklearn']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install pandas matplotlib seaborn numpy scikit-learn")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def 检查数据文件():
    """检查数据文件是否存在"""
    data_files = [
        'data/Data/features_30_sec.csv',
        'data/Data/features_3_sec.csv'
    ]
    
    missing_files = []
    for file in data_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少以下数据文件: {', '.join(missing_files)}")
        print("请确保GTZAN数据集已正确放置在data/Data/目录中")
        return False
    
    print("✅ 数据文件检查通过")
    return True

def 运行脚本(script_name, description):
    """运行指定的脚本"""
    print(f"\n🚀 开始{description}...")
    print("=" * 60)

    try:
        # 使用更简单的方式运行脚本，避免编码问题
        result = subprocess.run([sys.executable, script_name],
                              capture_output=False, text=True)

        if result.returncode == 0:
            print(f"✅ {description}完成")
        else:
            print(f"❌ {description}失败，返回码: {result.returncode}")
            return False

    except Exception as e:
        print(f"❌ 运行{script_name}时出错: {str(e)}")
        return False

    return True

def 统计生成结果():
    """统计生成的图表文件"""
    if not os.path.exists("中文图表"):
        print("❌ 中文图表目录不存在")
        return
    
    import glob
    图表文件 = glob.glob("中文图表/*.png")
    
    print(f"\n📊 图表生成统计:")
    print(f"   共生成 {len(图表文件)} 个图表文件")
    
    if len(图表文件) >= 16:
        print("✅ 所有16个图表已成功生成！")
    else:
        print(f"⚠️  预期16个图表，实际生成{len(图表文件)}个")
    
    print("\n📁 生成的图表文件:")
    for i, 文件 in enumerate(sorted(图表文件), 1):
        文件名 = os.path.basename(文件)
        print(f"   {i:2d}. {文件名}")

def main():
    """主函数"""
    print("🎵 音乐流派分类 - 中文图表生成器")
    print("=" * 60)
    print("本脚本将生成完整的16个中文图表，包括：")
    print("• 第一部分（图1-8）：基础分析图表")
    print("• 第二部分（图9-16）：实验结果图表")
    print("=" * 60)
    
    # 检查环境
    if not 检查依赖():
        return
    
    if not 检查数据文件():
        return
    
    print("\n🎯 开始生成图表...")
    
    # 记录开始时间
    start_time = time.time()
    
    # 运行第一部分脚本
    success1 = 运行脚本("生成中文图表_第一部分.py", "第一部分图表生成（图1-8）")
    
    if not success1:
        print("❌ 第一部分生成失败，停止执行")
        return
    
    # 运行第二部分脚本
    success2 = 运行脚本("生成中文图表_第二部分.py", "第二部分图表生成（图9-16）")
    
    if not success2:
        print("❌ 第二部分生成失败")
        return
    
    # 计算总耗时
    end_time = time.time()
    total_time = end_time - start_time
    
    # 统计结果
    统计生成结果()
    
    print(f"\n⏱️  总耗时: {total_time:.1f} 秒")
    print("\n🎉 所有中文图表生成完成！")
    print("\n📖 使用建议:")
    print("   1. 查看 '完整中文图表使用指南.md' 了解详细说明")
    print("   2. 图表文件位于 '中文图表/' 目录")
    print("   3. 所有图表均为300 DPI高分辨率，适合学术论文")
    print("   4. 图表标签和注释均为中文，可直接用于中文论文")
    
    print("\n📋 论文插入建议:")
    print("   • 图1-5: 数据集和预处理部分")
    print("   • 图6-8: 方法和模型设计部分") 
    print("   • 图9-12: 实验结果部分")
    print("   • 图13-15: 特征分析部分")
    print("   • 图16: 结论总结部分")

if __name__ == "__main__":
    main()
